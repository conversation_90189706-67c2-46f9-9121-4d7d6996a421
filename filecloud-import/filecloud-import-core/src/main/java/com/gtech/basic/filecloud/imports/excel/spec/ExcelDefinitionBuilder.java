package com.gtech.basic.filecloud.imports.excel.spec;

import com.gtech.basic.filecloud.imports.core.spec.FileColumn;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Builder of {@link ExcelDefinition}.
 *
 * <AUTHOR>
 * @since 1.2
 */
public class ExcelDefinitionBuilder {

    private final String category;
    private final List<SheetDefinitionBuilder> sheets;
    private final Map<String, Object> attachments;

    public ExcelDefinitionBuilder(String category) {
        Assert.hasLength(category, "Parameter 'category' must not be empty.");
        this.category = category;
        this.sheets = new ArrayList<>();
        this.attachments = new HashMap<>();
    }

    public SheetDefinitionBuilder sheet(String subcategory) {
        Assert.hasLength(subcategory, "Parameter 'subcategory' must not be empty.");
        SheetDefinitionBuilder sheetBuilder = new SheetDefinitionBuilder(this, subcategory);
        this.sheets.add(sheetBuilder);
        return sheetBuilder;
    }

    public ExcelDefinitionBuilder attachment(String key, Object attachment) {
        Assert.hasLength(key, "Parameter 'key' must not be empty.");
        this.attachments.put(key, attachment);
        return this;
    }

    public DefaultExcelDefinition build() {
        Objects.requireNonNull(category);
        Objects.requireNonNull(sheets);
        Assert.notEmpty(sheets, "The sheets of the excel are undefined.");

        List<SheetDefinition> sheetsDefinitions = sheets.stream()
                .map(SheetDefinitionBuilder :: buildSheet)
                .collect(Collectors.toList());
        return new DefaultExcelDefinition(this.category, sheetsDefinitions, this.attachments);
    }
    
    public static class SheetDefinitionBuilder {

        private final ExcelDefinitionBuilder parent;
        private final String subcategory;
        private final List<FileColumn> columns;
        private final Map<String, String> metadata;

        SheetDefinitionBuilder(ExcelDefinitionBuilder parent, String subcategory) {
            Objects.requireNonNull(parent);
            Objects.requireNonNull(subcategory);
            this.parent = parent;
            this.subcategory = subcategory;
            this.columns = new ArrayList<>();
            this.metadata = new HashMap<>();
        }

        public SheetDefinitionBuilder numberColumn(String name) {
            return column(name, FileColumn.DataType.NUMBER);
        }

        public SheetDefinitionBuilder stringColumn(String name) {
            return column(name, FileColumn.DataType.STRING);
        }

        public SheetDefinitionBuilder richTextHtmlColumn(String name) {
            return column(name, FileColumn.DataType.RICH_TEXT_HTML);
        }

        public SheetDefinitionBuilder richTextRtfColumn(String name) {
            return column(name, FileColumn.DataType.RICH_TEXT_RTF);
        }

        public SheetDefinitionBuilder richTextMarkdownColumn(String name) {
            return column(name, FileColumn.DataType.RICH_TEXT_MARKDOWN);
        }

        public SheetDefinitionBuilder column(String name, FileColumn.DataType dataType) {
            Objects.requireNonNull(name);
            Objects.requireNonNull(dataType);
            this.columns.add(FileColumn.Default.of(name, dataType));
            return this;
        }

        public SheetDefinitionBuilder column(String name, String dataType) {
            return column(name, FileColumn.DataType.resolve(dataType));
        }

        public SheetDefinitionBuilder columns(Collection<? extends FileColumn> fileColumns) {
            Objects.requireNonNull(fileColumns);
            this.columns.addAll(fileColumns);
            return this;
        }

        public SheetDefinitionBuilder metadata(String name, String value) {
            Objects.requireNonNull(name);
            this.metadata.put(name, value);
            return this;
        }

        public SheetDefinitionBuilder metadata(Map<String, String> metadata) {
            if (CollectionUtils.isEmpty(metadata)) {
                return this;
            }

            this.metadata.putAll(metadata);
            return this;
        }

        public SheetDefinitionBuilder nextSheet(String subcategory) {
            return this.parent.sheet(subcategory);
        }

        private DefaultSheetDefinition buildSheet() {
            return new DefaultSheetDefinition(this.subcategory, this.columns, this.metadata);
        }

        public DefaultExcelDefinition build() {
            return parent.build();
        }
    }
}
